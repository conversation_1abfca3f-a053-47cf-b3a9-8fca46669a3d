import styled from "styled-components";
import { useIsMobile } from "../util/isMobile";

interface MobileResponsive {
  $mobile: boolean;
}

const HomeHeader = styled.div<MobileResponsive>`
  width: 100%;
  height: ${(props) => (props.$mobile ? "240px" : "320px")};
`;

const HeaderGradient = styled.div<MobileResponsive>`
  width: 100%;
  height: ${(props) => (props.$mobile ? "240px" : "320px")};
  background: rgba(0, 0, 0, 0);
  background: linear-gradient(
    0deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, .6) ${(props) => (props.$mobile ? "20" : "12")}%,
    rgba(255, 255, 255, 0) ${(props) => (props.$mobile ? "20" : "12")}%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateY(-100%);
`;

const HeaderImage = styled.div<MobileResponsive>`
  width: 100%;
  height: ${(props) => (props.$mobile ? "240px" : "320px")};
  background-image: url("/asset/cover_large.jpg");
  background-size: cover;
  background-position: ${(props) => (props.$mobile ? "left-center" : "center")};
`;

const Title = styled.div<MobileResponsive>`
  margin-top: -48px;
  font-size: ${(props) => (props.$mobile ? "48px" : "96px")};
  position: absolute;
`;

const Subtitle = styled.div<MobileResponsive>`
  font-size: ${(props) => (props.$mobile ? "24px" : "48px")};
  margin-top: ${(props) => (props.$mobile ? "-6px" : "32px")};
  margin-bottom: 36px;
`;

const Summary = styled.div<MobileResponsive>`
  font-size: ${(props) => (props.$mobile ? "21px" : "24px")};
`;

const H2 = styled.div<MobileResponsive>`
  font-size: ${(props) => (props.$mobile ? "24px" : "32px")};
  margin-top: ${(props) => (props.$mobile ? "24px" : "48px")};
  margin-bottom: 24px;
`;

const Body1 = styled.div<MobileResponsive>`
  font-size: ${(props) => (props.$mobile ? "12px" : "16px")};
  margin-bottom: 24px;
`;

const LinkOut = styled.a`
  color: #195959;
  text-decoration: none;
  &:hover {
    font-weight: 600;
  }
`;

const LinkContainer = styled.a`
  text-decoration: none;
  color: #191919;
  &:hover {
    font-weight: 600;
  }
`;

const FeatureRow = styled.div<MobileResponsive>`
  display: flex;
  flex-direction: row;
  gap: 16px;
  margin-bottom: ${(props) => (props.$mobile ? "0px" : "16px")};
  padding-right: ${(props) => (props.$mobile ? "0px" : "168px")};
`;

const FeatureCover = styled.div<MobileResponsive>`
  width: ${(props) => (props.$mobile ? "96px" : "192px")};
  height: 96px;
  background: #c9c9c9;
  border-radius: 4px;
  border: 2px solid #c9c9c9; 
  background-size: cover;
  background-position: center;
  flex-shrink: 0;
`;

const FeatureTitle = styled.div<MobileResponsive>`
  font-size: ${(props) => (props.$mobile ? "20px" : "24px")};
  margin-bottom: 8px;
`;

const HalfRow = styled.div`
  flex: 1;
`;

const HomeContainer = styled.div<MobileResponsive>`
  width: ${(props) => (props.$mobile ? "calc(100% - 32px)" : "840px")};
  margin: auto;
  text-align: left;
  color: #191919;
  user-select: none;
  padding-bottom: ${(props) => (props.$mobile ? "32px" : "128px")};
  overflow: hidden;
`;

const Home = () => {
  const { isMobile } = useIsMobile();

  return (
    <HomeContainer $mobile={isMobile}>
      <HomeHeader $mobile={isMobile}>
        <HeaderImage $mobile={isMobile} />
        <HeaderGradient $mobile={isMobile} />
      </HomeHeader>
      <Title $mobile={isMobile}>Peter Gorgenyi</Title>
      <Subtitle $mobile={isMobile}>@peterwritescode</Subtitle>
      <Summary $mobile={isMobile}>
        Expert in front-end application development and architecture.
        <br />
        Specialized in React stacks. Innovator, architect, leader.
        <br />
        Mentor of front-end teams. Partner to product.
      </Summary>
      <FeatureRow $mobile={isMobile}>
        <HalfRow>
          <H2 $mobile={isMobile}>Resume</H2>
          <Body1 $mobile={isMobile}>
            <LinkOut
              href="/asset/Peter%20Gorgenyi%20Resume.pdf"
              target="_blank"
            >
              Download resume as PDF
            </LinkOut>
          </Body1>
        </HalfRow>
        <HalfRow>
          <H2 $mobile={isMobile}>Contact</H2>
          <Body1 $mobile={isMobile}>peterwritescode -at- gmail</Body1>
        </HalfRow>
        <HalfRow>
          <H2 $mobile={isMobile}>GitHub</H2>
          <Body1 $mobile={isMobile}>
            <LinkOut
              href="https://github.com/peter-writes-code"
              target="_blank"
            >
              peter-writes-code
            </LinkOut>
          </Body1>
        </HalfRow>
      </FeatureRow>
      <H2 $mobile={isMobile}>Demo / Open Source</H2>
      <LinkContainer href="https://ze-bugs.netlify.app/" target="_blank">
        <FeatureRow $mobile={isMobile}>
          <FeatureCover
            $mobile={isMobile}
            style={{ backgroundImage: `url("/asset/ze_bugs.png")` }}
          />
          <div>
            <FeatureTitle $mobile={isMobile}>Ze Bugs</FeatureTitle>
            <Body1 $mobile={isMobile}>
              An experimental open source React project. The ambition is to
              explore exceptional patterns and performance.
            </Body1>
          </div>
        </FeatureRow>
      </LinkContainer>
      <LinkContainer href="https://tiles-2025.netlify.app/" target="_blank">
        <FeatureRow $mobile={isMobile}>
          <FeatureCover
            $mobile={isMobile}
            style={{ backgroundImage: `url("/asset/tiles.png")` }}
          />
          <div>
            <FeatureTitle $mobile={isMobile}>Tiles</FeatureTitle>
            <Body1 $mobile={isMobile}>
              An open source react/redux dynamic gallery. A simplistic example
              demonstrating advanced react patterns and UI finesse.
            </Body1>
          </div>
        </FeatureRow>
      </LinkContainer>
      <H2 $mobile={isMobile}>Videos</H2>
      <LinkContainer href="https://youtu.be/D2mqqqs7hLo" target="_blank">
        <FeatureRow $mobile={isMobile}>
          <FeatureCover
            $mobile={isMobile}
            style={{ backgroundImage: `url("/asset/scaleable1.png")` }}
          />
          <div>
            <FeatureTitle $mobile={isMobile}>
              Scalable Front End through Layered Architecture
            </FeatureTitle>
            <Body1 $mobile={isMobile}>Pt 1 - Introduction and Overview</Body1>
          </div>
        </FeatureRow>
      </LinkContainer>
      <LinkContainer href="https://youtu.be/7e59zsy45I4" target="_blank">
        <FeatureRow $mobile={isMobile}>
          <FeatureCover
            $mobile={isMobile}
            style={{ backgroundImage: `url("/asset/scalable2.png")` }}
          />
          <div>
            <FeatureTitle $mobile={isMobile}>
              Scalable Front End through Layered Architecture
            </FeatureTitle>
            <Body1 $mobile={isMobile}>Pt 2 - Layers and Their Roles</Body1>
          </div>
        </FeatureRow>
      </LinkContainer>{" "}
      <H2 $mobile={isMobile}>Articles</H2>
      <LinkContainer
        href="https://medium.com/@peterwritescode/scalable-front-end-through-layered-architecture-232ffb32da72"
        target="_blank"
      >
        <FeatureRow $mobile={isMobile}>
          <FeatureCover
            $mobile={isMobile}
            style={{ backgroundImage: `url("/asset/scaleable1.png")` }}
          />
          <div>
            <FeatureTitle $mobile={isMobile}>
              Scalable Front End through Layered Architecture
            </FeatureTitle>
            <Body1 $mobile={isMobile}>Pt 1 - Introduction and Overview</Body1>
          </div>
        </FeatureRow>
      </LinkContainer>
      <LinkContainer
        href="https://medium.com/@peterwritescode/scalable-front-end-through-layered-architecture-pt-2-layers-and-their-roles-7df19fa94dee"
        target="_blank"
      >
        <FeatureRow $mobile={isMobile}>
          <FeatureCover
            $mobile={isMobile}
            style={{ backgroundImage: `url("/asset/scalable2.png")` }}
          />
          <div>
            <FeatureTitle $mobile={isMobile}>
              Scalable Front End through Layered Architecture
            </FeatureTitle>
            <Body1 $mobile={isMobile}>Pt 2 - Layers and Their Roles</Body1>
          </div>
        </FeatureRow>
      </LinkContainer>
    </HomeContainer>
  );
};

export default Home;
