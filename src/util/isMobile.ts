import { useEffect, useState } from "react";

interface UseIsMobile {
  isMobile: boolean;
}

function getIsMobile(): boolean {
  return /Android|iPhone|iPad|iPod|webOS|BlackBerry|Mobi/i.test(
    navigator.userAgent
  );
}

export function useIsMobile(): UseIsMobile {
  const [isMobile, setIsMobile] = useState(getIsMobile());
  const handleResize = () => {
    setIsMobile(getIsMobile());
  };

  useEffect(() => {
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);
  return { isMobile };
}
