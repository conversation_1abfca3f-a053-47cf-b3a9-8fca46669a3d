<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:opsz@14..32&display=swap"
      rel="stylesheet"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Peter Gorgenyi | @peterwritescode</title>
  </head>
  <body>
    <noscript>
      <style>
        /* Base styles for no-JS fallback */
        .noscript-container {
          width: 840px;
          margin: auto;
          text-align: left;
          color: #191919;
          user-select: none;
          padding-bottom: 128px;
          overflow: hidden;
        }

        .noscript-header {
          width: 100%;
          height: 320px;
        }

        .noscript-header-image {
          width: 100%;
          height: 320px;
          background-image: url("/asset/cover_large.jpg");
          background-size: cover;
          background-position: center;
        }

        .noscript-header-gradient {
          width: 100%;
          height: 320px;
          background: rgba(0, 0, 0, 0);
          background: linear-gradient(
            0deg,
            rgba(255, 255, 255, 1) 0%,
            rgba(255, 255, 255, .6) 12%,
            rgba(255, 255, 255, 0) 12%,
            rgba(255, 255, 255, 0) 100%
          );
          transform: translateY(-100%);
        }

        .noscript-title {
          margin-top: -48px;
          font-size: 96px;
          position: absolute;
        }

        .noscript-subtitle {
          font-size: 48px;
          margin-top: 32px;
          margin-bottom: 36px;
        }

        .noscript-summary {
          font-size: 24px;
        }

        .noscript-h2 {
          font-size: 32px;
          margin-top: 48px;
          margin-bottom: 24px;
        }

        .noscript-body1 {
          font-size: 16px;
          margin-bottom: 24px;
        }

        .noscript-link-out {
          color: #195959;
          text-decoration: none;
        }

        .noscript-link-out:hover {
          font-weight: 600;
        }

        .noscript-link-container {
          text-decoration: none;
          color: #191919;
        }

        .noscript-link-container:hover {
          font-weight: 600;
        }

        .noscript-feature-row {
          display: flex;
          flex-direction: row;
          gap: 16px;
          margin-bottom: 16px;
          padding-right: 168px;
        }

        .noscript-feature-cover {
          width: 192px;
          height: 96px;
          background: #c9c9c9;
          border-radius: 4px;
          border: 2px solid #c9c9c9;
          background-size: cover;
          background-position: center;
          flex-shrink: 0;
        }

        .noscript-feature-title {
          font-size: 24px;
          margin-bottom: 8px;
        }

        .noscript-half-row {
          flex: 1;
        }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
          .noscript-container {
            width: calc(100% - 32px);
            padding-bottom: 32px;
          }

          .noscript-header {
            height: 240px;
          }

          .noscript-header-image {
            height: 240px;
            background-position: left-center;
          }

          .noscript-header-gradient {
            height: 240px;
            background: linear-gradient(
              0deg,
              rgba(255, 255, 255, 1) 0%,
              rgba(255, 255, 255, .6) 20%,
              rgba(255, 255, 255, 0) 20%,
              rgba(255, 255, 255, 0) 100%
            );
          }

          .noscript-title {
            font-size: 48px;
          }

          .noscript-subtitle {
            font-size: 24px;
            margin-top: -6px;
          }

          .noscript-summary {
            font-size: 21px;
          }

          .noscript-h2 {
            font-size: 24px;
            margin-top: 24px;
          }

          .noscript-body1 {
            font-size: 12px;
          }

          .noscript-feature-row {
            margin-bottom: 0px;
            padding-right: 0px;
          }

          .noscript-feature-cover {
            width: 96px;
          }

          .noscript-feature-title {
            font-size: 20px;
          }
        }
      </style>

      <div class="noscript-container">
        <div class="noscript-header">
          <div class="noscript-header-image"></div>
          <div class="noscript-header-gradient"></div>
        </div>
        <div class="noscript-title">Peter Gorgenyi</div>
        <div class="noscript-subtitle">@peterwritescode</div>
        <div class="noscript-summary">
          Expert in front-end application development and architecture.<br>
          Specialized in React stacks. Innovator, architect, leader.<br>
          Mentor of front-end teams. Partner to product.
        </div>
        <div class="noscript-feature-row">
          <div class="noscript-half-row">
            <div class="noscript-h2">Resume</div>
            <div class="noscript-body1">
              <a href="/asset/Peter%20Gorgenyi%20Resume.pdf" target="_blank" class="noscript-link-out">
                Download resume as PDF
              </a>
            </div>
          </div>
          <div class="noscript-half-row">
            <div class="noscript-h2">Contact</div>
            <div class="noscript-body1">peterwritescode -at- gmail</div>
          </div>
          <div class="noscript-half-row">
            <div class="noscript-h2">GitHub</div>
            <div class="noscript-body1">
              <a href="https://github.com/peter-writes-code" target="_blank" class="noscript-link-out">
                peter-writes-code
              </a>
            </div>
          </div>
        </div>
        <div class="noscript-h2">Demo / Open Source</div>
        <a href="https://ze-bugs.netlify.app/" target="_blank" class="noscript-link-container">
          <div class="noscript-feature-row">
            <div class="noscript-feature-cover" style="background-image: url('/asset/ze_bugs.png')"></div>
            <div>
              <div class="noscript-feature-title">Ze Bugs</div>
              <div class="noscript-body1">
                An experimental open source React project. The ambition is to
                explore exceptional patterns and performance.
              </div>
            </div>
          </div>
        </a>
        <a href="https://tiles-2025.netlify.app/" target="_blank" class="noscript-link-container">
          <div class="noscript-feature-row">
            <div class="noscript-feature-cover" style="background-image: url('/asset/tiles.png')"></div>
            <div>
              <div class="noscript-feature-title">Tiles</div>
              <div class="noscript-body1">
                An open source react/redux dynamic gallery. A simplistic example
                demonstrating advanced react patterns and UI finesse.
              </div>
            </div>
          </div>
        </a>
        <div class="noscript-h2">Videos</div>
        <a href="https://youtu.be/D2mqqqs7hLo" target="_blank" class="noscript-link-container">
          <div class="noscript-feature-row">
            <div class="noscript-feature-cover" style="background-image: url('/asset/scaleable1.png')"></div>
            <div>
              <div class="noscript-feature-title">
                Scalable Front End through Layered Architecture
              </div>
              <div class="noscript-body1">Pt 1 - Introduction and Overview</div>
            </div>
          </div>
        </a>
        <a href="https://youtu.be/7e59zsy45I4" target="_blank" class="noscript-link-container">
          <div class="noscript-feature-row">
            <div class="noscript-feature-cover" style="background-image: url('/asset/scalable2.png')"></div>
            <div>
              <div class="noscript-feature-title">
                Scalable Front End through Layered Architecture
              </div>
              <div class="noscript-body1">Pt 2 - Layers and Their Roles</div>
            </div>
          </div>
        </a>
        <div class="noscript-h2">Articles</div>
        <a href="https://medium.com/@peterwritescode/scalable-front-end-through-layered-architecture-232ffb32da72" target="_blank" class="noscript-link-container">
          <div class="noscript-feature-row">
            <div class="noscript-feature-cover" style="background-image: url('/asset/scaleable1.png')"></div>
            <div>
              <div class="noscript-feature-title">
                Scalable Front End through Layered Architecture
              </div>
              <div class="noscript-body1">Pt 1 - Introduction and Overview</div>
            </div>
          </div>
        </a>
        <a href="https://medium.com/@peterwritescode/scalable-front-end-through-layered-architecture-pt-2-layers-and-their-roles-7df19fa94dee" target="_blank" class="noscript-link-container">
          <div class="noscript-feature-row">
            <div class="noscript-feature-cover" style="background-image: url('/asset/scalable2.png')"></div>
            <div>
              <div class="noscript-feature-title">
                Scalable Front End through Layered Architecture
              </div>
              <div class="noscript-body1">Pt 2 - Layers and Their Roles</div>
            </div>
          </div>
        </a>
      </div>
    </noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
