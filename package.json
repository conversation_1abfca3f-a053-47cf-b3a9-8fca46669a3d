{"name": "peterwritescode", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "13.5.0", "@types/jest": "27.5.2", "@types/node": "16.18.126", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "styled-components": "^6.1.19", "typescript": "4.9.5", "uuid": "^11.1.0", "web-vitals": "2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/styled-components": "^5.1.34"}}